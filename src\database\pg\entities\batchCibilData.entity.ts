// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { registeredUsers } from './registeredUsers';
import { BatchCibilFileTrackingEntity } from './batchCibilFileTracking.entity';

@Table({})
export class BatchCibilDataEntity extends Model<BatchCibilDataEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({ type: DataType.UUID, allowNull: true })
  userId: string;

  @BelongsTo(() => registeredUsers)
  userData: registeredUsers;

  @Column({ type: DataType.STRING, allowNull: true })
  fullName: string;

  @Column({ type: DataType.INTEGER, allowNull: true })
  cibilScore: number;

  @Column({ type: DataType.INTEGER, allowNull: true })
  plScore: number;

  @ForeignKey(() => BatchCibilFileTrackingEntity)
  @Column({ type: DataType.INTEGER, allowNull: false })
  fileId: string;

  @BelongsTo(() => BatchCibilFileTrackingEntity)
  fileData: BatchCibilFileTrackingEntity;
}
