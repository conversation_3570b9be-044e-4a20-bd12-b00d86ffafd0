// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { registeredUsers } from './registeredUsers';
import { loanTransaction } from './loanTransaction';

@Table({})
export class CibilScoreEntity extends Model<CibilScoreEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.ENUM,
    defaultValue: '0',
    values: ['0', '1', '2', '3', '4'],
    allowNull: false,
    comment: '0=Pending 1=Success 2=Failed, 3=Error, 4=Unknown',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  cibilScore: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  plScore: number;

  @Column({
    type: DataType.JSONB,
    allowNull: true,
  })
  responsedata: { consumerCreditData: any[] };

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  zeroBalanceAccounts: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  inquiryPast30Days: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  overdueBalance: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  totalOverdueDays: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  PLOutstanding: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  PLAccounts: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  highCreditAmount: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  currentBalance: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => registeredUsers, {
    foreignKey: 'userId',
    targetKey: 'id',
    constraints: false,
  })
  registeredUsers: registeredUsers;

  @ForeignKey(() => loanTransaction)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  loanId: number;
}
