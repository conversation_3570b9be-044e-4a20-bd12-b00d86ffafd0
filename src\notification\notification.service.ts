import { HttpStatus, Injectable } from '@nestjs/common';
import { HTTPError, raiseParamMissing } from 'src/config/error';
import { NUMBERS } from 'src/constant/objects';
import { TemplateEntity } from 'src/database/pg/entities/template.entity';
import { PgService } from 'src/database/pg/pg.service';
import { RedisService } from 'src/database/redis/redis.service';

@Injectable()
export class NotificationService {
  constructor(
    private readonly pg: PgService,
    private readonly redisService: RedisService,
  ) {}

  async getTemplatesList() {
    const redisKey = 'CHAT_MSG_TEMPLATES';
    const redisData = await this.redisService.getString(redisKey);
    if (redisData) return JSON.parse(redisData);
    const where: any = { type: 'CHAT_SUPPORT', isActive: true };
    const templateData = await this.pg.findAll(TemplateEntity, {
      where,
      attributes: ['id', 'title', 'content'],
      order: [['title']],
    });
    if (!templateData)
      throw new HTTPError({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'No Data Found',
      });
    await this.redisService.setString(
      redisKey,
      JSON.stringify(templateData),
      NUMBERS.SEVEN_DAYS_IN_SECONDS,
    );
    return templateData;
  }
  async manageTemplate(body) {
    const { type, id, title, content, adminId } = body;
    const redisKey = 'CHAT_MSG_TEMPLATES';
    if (!type) raiseParamMissing('required type');
    if (!adminId) raiseParamMissing('required adminId');
    if (type == 'CREATE') {
      if (!title || !content)
        raiseParamMissing('title or content are required');
      const existing: any[] = await this.pg.findAll(TemplateEntity, {
        where: { type: 'CHAT_SUPPORT', isActive: true },
      });
      let caseExisting = false;
      for (const t of existing) {
        if (t.title.toLowerCase() === title.toLowerCase()) {
          caseExisting = true;
          break;
        }
      }
      if (caseExisting)
        throw new HTTPError({
          statusCode: HttpStatus.BAD_REQUEST,
          message: `title ${title} already exists`,
        });
      const data = {
        title,
        content,
        type: 'CHAT_SUPPORT',
        isActive: true,
        adminId,
      };
      const createData = await this.pg.create(TemplateEntity, data);
      await this.redisService.delKey(redisKey);
      return createData;
    }
    if (type == 'UPDATE') {
      if (!id) raiseParamMissing('id is required');
      if (!title || !content)
        raiseParamMissing('title or content are required');
      const updateData: any = { title, content, adminId };
      const updated = await this.pg.update(TemplateEntity, updateData, {
        where: { id, type: 'CHAT_SUPPORT' },
      });
      if (updated.count === 0)
        throw new HTTPError({
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Template not found',
        });
      await this.redisService.delKey(redisKey);
      return updated.updated[0];
    }
    if (type == 'DELETE') {
      if (!id) raiseParamMissing('id is required');
      const deleteData = await this.pg.update(
        TemplateEntity,
        { isActive: false },
        { where: { id, type: 'CHAT_SUPPORT', isActive: true } },
      );
      if (deleteData.count === 0)
        throw new HTTPError({
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Template not found or already deleted',
        });

      await this.redisService.delKey(redisKey);
      return { message: 'Success' };
    }
  }
}
