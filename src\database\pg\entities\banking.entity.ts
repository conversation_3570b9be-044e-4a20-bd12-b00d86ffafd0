// Imports
import { Table, Column, Model, DataType } from 'sequelize-typescript';

@Table({})
export class BankingEntity extends Model<BankingEntity> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({ type: DataType.STRING, allowNull: true })
  bank: string;

  @Column({ type: DataType.STRING, allowNull: true })
  accountNumber: string;

  @Column({ type: DataType.INTEGER })
  adminId: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  salary: number;

  @Column({ type: DataType.DOUBLE, allowNull: true })
  adminSalary: number;

  @Column({ type: DataType.JSONB, allowNull: true, defaultValue: {} })
  otherDetails: any;

  @Column({
    type: DataType.SMALLINT,
    allowNull: true,
    comment: '1 -> Requested, 2 -> Failed, 3 -> Fetched',
  })
  aaDataStatus: number;
}
