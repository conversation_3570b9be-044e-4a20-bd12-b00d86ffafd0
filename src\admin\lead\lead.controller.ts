// Imports
import { raiseParamMissing } from 'src/config/error';
import { LeadService } from './lead.service';
import { Controller, Get, Query, Req } from '@nestjs/common';

@Controller('admin/lead')
export class LeadController {
  constructor(private readonly service: LeadService) {}

  @Get('leadPriorityList')
  async funLeadPriorityList(@Query() query) {
    return await this.service.leadPriorityList(query);
  }

  //#region Stagewise Leads
  @Get('stagewiseLeads')
  async funStagewiseLeads(@Query() query, @Req() req) {
    const apiUrl: string = req.route.path.toLowerCase().trim();
    query.apiUrl = apiUrl;
    const adminId = req.headers.adminid;
    if (!adminId) raiseParamMissing('Header adminId');
    query.headerAdminId = adminId;
    return await this.service.stagewiseLeads(query);
  }
  //#endregion
}
