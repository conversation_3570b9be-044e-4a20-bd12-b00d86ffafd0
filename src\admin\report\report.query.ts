// Imports
import { Injectable } from '@nestjs/common';
import { DateService } from 'src/utils/date.service';
import { PgService } from 'src/database/pg/pg.service';
import { CSE_ROLE_ID, kGlobalDateTrail } from 'src/constant/global';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { raiseBadRequest, raiseParamMissing } from 'src/config/error';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { crmActivity } from 'src/database/pg/entities/crmActivity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { admin } from 'src/database/pg/entities/admin';
import { MasterEntity } from 'src/database/pg/entities/master.entity';
import { ReportHistoryEntity } from 'src/database/pg/entities/reportHistory.entity';

@Injectable()
export class ReportQueryService {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
  ) {}

  async dataForCollectionEfficiency(reqData) {
    let start_date: string = reqData.start_date;
    if (!start_date) raiseParamMissing('start_date');
    let end_date: string = reqData.end_date;
    if (!end_date) raiseParamMissing('end_date');
    start_date = start_date.substring(0, 10) + kGlobalDateTrail;
    end_date = end_date.substring(0, 10) + kGlobalDateTrail;

    const current_month_emi_query = `SELECT "emi_date", "loanId", "principalCovered",
    "id", "interestCalculate", "pay_type", "payment_due_status", "payment_done_date"  
    FROM "EmiEntities"

    WHERE ("emi_date" BETWEEN '${start_date}' AND '${end_date}') AND 
    ("payment_done_date" IS NULL OR "payment_done_date" >= '${start_date}')
    `;

    const currentMonthEmiList: EmiEntity[] = await this.pg.query(
      current_month_emi_query,
    );

    const currentMonthEmiIds = currentMonthEmiList.map((el) => el.id);
    const current_month_payment_query = `SELECT "emiId", "principalAmount", "interestAmount", "maxDPD"
    FROM "TransactionEntities" WHERE ("completionDate" BETWEEN '${start_date}' AND '${end_date}') AND
    "status" = 'COMPLETED' AND "emiId" IN (${currentMonthEmiIds.join(',')})`;

    const currentMonthPayments: TransactionEntity[] = await this.pg.query(
      current_month_payment_query,
    );
    const currentMonthPaymentData = {};
    const current_month_emis = [];
    currentMonthPayments.forEach((el) => {
      if (!currentMonthPaymentData[el['emiId']]) {
        current_month_emis.push(el['emiId']);
        currentMonthPaymentData[el['emiId']] = [];
      }

      currentMonthPaymentData[el['emiId']].push(el);
    });

    let current_month_od_paid_query = `SELECT "principalAmount", "interestAmount" 
    FROM "TransactionEntities"
    WHERE ("completionDate" BETWEEN '${start_date}' AND '${end_date}') AND
    "status" = 'COMPLETED' AND "maxDPD" != 0 AND "emiId" NOT IN (${current_month_emis.join(',')})`;
    const od_paid_transaction_list = await this.pg.query(
      current_month_od_paid_query,
    );

    let carry_forward_od_principal_paid = 0;
    let carry_forward_od_interest_paid = 0;
    od_paid_transaction_list.forEach((el) => {
      carry_forward_od_principal_paid += el.principalAmount;
      carry_forward_od_interest_paid += el.interestAmount;
    });

    return {
      currentMonthEmiList,
      currentMonthPaymentData,
      carry_forward_od_principal_paid,
      carry_forward_od_interest_paid,
    };
  }

  async dataForCsePerformanceReport(reqData) {
    const {
      minRange,
      crmMinRange,
      maxRange,
      needRawData,
      loanType,
      isDailyReport,
    } = reqData;

    let admin_query = `SELECT "id", "fullName", "roleId" , "isActive" FROM "admins"`;
    if (!needRawData) admin_query += `WHERE "roleId" = ${CSE_ROLE_ID};`;

    const adminData: admin[] = await this.pg.query(admin_query);

    let cseAdminIds = [];
    if (!needRawData) cseAdminIds = adminData?.map((ele) => ele.id);
    else
      cseAdminIds = adminData
        .filter((ele) => ele?.roleId == CSE_ROLE_ID)
        ?.map((ele) => ele.id);

    const loanAttributes = [
      'id',
      'loanStatus',
      'netApprovedAmount',
      'loanDisbursementDateTime',
      'loanRejectDateTime',
      'createdAt',
    ];
    if (needRawData)
      loanAttributes.push(
        ...[
          'userId',
          'fullName',
          'phone',
          'completedLoan',
          'manualVerificationAcceptId',
        ],
      );

    let loan_query = `SELECT ${loanAttributes.map((ele) => `"${ele}"`).join(',')}
    FROM "loanTransactions"
	  WHERE
    (("loanDisbursementDateTime" >= '${minRange.toJSON()}' AND "loanDisbursementDateTime" <= '${maxRange.toJSON()}') 
    OR 
	  ("loanRejectDateTime" >= '${minRange.toJSON()}' AND "loanRejectDateTime" <= '${maxRange.toJSON()}')`;

    if (isDailyReport)
      loan_query += ` OR "loanStatus" IN ('InProcess','Accepted'))`;
    else
      loan_query += ` OR ("createdAt" >= '${minRange.toJSON()}' AND "createdAt" <= '${maxRange.toJSON()}'))`;

    if (loanType == '0') loan_query += ` AND ("completedLoan" > 0)`;
    else if (loanType == '1')
      loan_query += ` AND ("completedLoan" = 0 OR "completedLoan" IS NULL)`;

    const loanData: loanTransaction[] = await this.pg.query(loan_query);

    let crmData: crmActivity[] = [];
    if (loanData.length && cseAdminIds.length) {
      const crm_query = `
      SELECT  "id", "userId", "createdAt", "loanId", "adminId",
      ("createdAt"::TIMESTAMPTZ AT TIME ZONE 'ASIA/KOLKATA')::DATE AS "createdDate"
      FROM "crmActivities"
      WHERE "id" IN (
      SELECT MAX("id") AS "maxCrmId" FROM "crmActivities"
      WHERE
        "createdAt" >= '${crmMinRange.toJSON()}' AND
        "createdAt" <= '${maxRange.toJSON()}' AND
        "loanId" IN (${loanData.map((ele) => `'${ele.id}'`).join(',')}) AND
        "adminId" IN (${cseAdminIds.map((id) => `'${id}'`).join(',')})
        GROUP BY "loanId"
      )`;
      crmData = await this.pg.query(crm_query);
    }

    let masterData: MasterEntity[] = [];
    if (loanData.length && needRawData) {
      const master_query = `SELECT "loanId","assignedCSE" FROM "MasterEntities" 
      WHERE "loanId" IN (${loanData.map((ele) => `'${ele.id}'`).join(',')})`;

      masterData = await this.pg.query(master_query);
    }

    return { adminData, crmData, loanData, masterData };
  }

  async dataForOverallAadhaarStateCollection(reqData) {
    const start_date = reqData.startDate;
    const end_date = reqData.endDate;
    const raw_query = `SELECT *
    FROM (SELECT "aadhaarState" AS "Aadhaar State",
          COUNT("LoanDetails"."loanId") AS "Total Due Loans",
            
        (SUM("expected_principal") + SUM("expected_interest")) AS "Expected EMI Amount",
          (SUM("LoanDetails"."paid_principal") + SUM("LoanDetails"."paid_interest")) AS "PAID EMI Amount",
        
        ROUND(CAST(CASE WHEN SUM("expected_principal") > 0
                    THEN ((SUM("LoanDetails"."paid_principal") +  SUM("LoanDetails"."paid_interest")) 
                / (SUM("expected_principal") + SUM("expected_interest")) ) * 100 ELSE 0
          END AS numeric), 2) AS "Overall Collection Efficiency (%)",

          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" = 0 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (0 DPD)",
          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" BETWEEN 1 AND 30 AND "LoanDetails"."paid_principal" > 0
            THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (1–30 DPD)",
          ROUND(COALESCE((SUM(CASE WHEN "max_dpd" BETWEEN 31 AND 90 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (31–90 DPD)",
        ROUND(COALESCE((SUM(CASE WHEN "max_dpd" > 90 AND "LoanDetails"."paid_principal" > 0
          THEN "LoanDetails"."paid_principal"::numeric END) / SUM("expected_principal")::numeric) * 100, 0), 2) AS "Collection Efficiency (90+ DPD)"
      
        FROM "LoanDetails"
      
      WHERE "expected_principal" > 0 AND "disbursed_date" >= '${start_date}' AND "disbursed_date" <= '${end_date}'
      
        GROUP BY "aadhaarState"
    ) AS subquery

    ORDER BY "Aadhaar State" ASC`;

    return await this.pg.query(raw_query);
  }

  async dataForTotalLoansForAadhaarStateCollection(reqData) {
    const start_date = reqData.startDate;
    const end_date = reqData.endDate;

    const raw_query = `SELECT "aadhaarState", CAST(COUNT("loanId") AS NUMERIC) AS "Total Loans",
    CAST(SUM(CASE WHEN "loanStatus" = '1' THEN 1 ELSE 0 END) AS NUMERIC) AS "Paid Loans" 
    FROM "LoanDetails"

    WHERE "disbursed_date" >= '${start_date}' AND "disbursed_date" <= '${end_date}'

    GROUP BY "aadhaarState" ORDER BY "aadhaarState" ASC`;

    return await this.pg.query(raw_query);
  }

  async dataForVintageReport(reqData) {
    const minRange = reqData.minRange;
    if (!minRange) {
      raiseParamMissing('minRange');
    }
    const maxRange = reqData.maxRange;
    if (!maxRange) {
      raiseParamMissing('maxRange');
    }
    const user_base = reqData.user_base;
    if (!user_base) {
      raiseParamMissing('user_base');
    }
    if (!['ALL', 'NEW', 'REPEAT'].includes(user_base)) {
      raiseBadRequest('Invalid value for user_base');
    }
    const debug_data = reqData.debug_data ?? {};
    const debug_loan_ids = debug_data.loan_ids ?? [];
    let debug_where_str = '';
    if (debug_loan_ids.length > 0) {
      debug_where_str += ` AND "loan"."id" IN ('${debug_loan_ids.join("','")}')`;
    }
    if (user_base == 'NEW') {
      debug_where_str += `AND "loan"."completedLoan" = '0'`;
    } else if (user_base == 'REPEAT') {
      debug_where_str += `AND "loan"."completedLoan" > '0'`;
    }

    let raw_query = `SELECT 
          TO_CHAR(DATE_TRUNC('month', loan_disbursement_date::date), 'Mon-YY') AS target_month
      FROM 
          "loanTransactions"
      WHERE
          loan_disbursement_date IS NOT NULL
      GROUP BY 
          DATE_TRUNC('month', loan_disbursement_date::date)
      ORDER BY 
          DATE_TRUNC('month', loan_disbursement_date::date);`;
    const date_range = await this.pg.query(raw_query);

    const finalized_months = [];
    let is_started = false;
    // Mapping the necessary month ranges
    for (let index = 0; index < date_range.length; index++) {
      const target_month = date_range[index].target_month;
      if (target_month == minRange) {
        is_started = true;
      }
      if (target_month == maxRange) {
        finalized_months.push(target_month);
        break;
      }

      if (!is_started) {
        continue;
      } else {
        finalized_months.push(target_month);
      }
    }

    raw_query = `SELECT "loan"."id", "loan_disbursement_date", 
    TO_CHAR(DATE_TRUNC('month', loan_disbursement_date::date), 'Mon-YY') AS "disb_month",
    CAST("netApprovedAmount" AS NUMERIC) AS "approved_amt" 
    FROM "loanTransactions" AS "loan"
    WHERE TO_CHAR(DATE_TRUNC('month', loan_disbursement_date::date), 'Mon-YY') IN ('${finalized_months.join("','")}')
    ${debug_where_str}
    
    ORDER BY TO_CHAR(DATE_TRUNC('month', loan_disbursement_date::date), 'Mon-YY')`;
    const target_disbursed_loans = await this.pg.query(raw_query);

    const loan_ids = target_disbursed_loans.map((el) => el.id);
    if (loan_ids.length == 0) {
      return {
        finalized_months,
        target_disbursed_loans,
        emi_list: [],
        trans_map: {},
      };
    }
    raw_query = `SELECT "emi"."emi_date", "emi"."loanId", "emi"."payment_done_date", "emi"."principalCovered", "emi"."id" 
    FROM "EmiEntities" AS "emi"
    WHERE "emi"."loanId" IN ('${loan_ids.join("','")}')`;
    const emi_list = await this.pg.query(raw_query);

    const emi_ids = emi_list.map((el) => el.id);
    raw_query = `SELECT "completionDate", "emiId", "principalAmount" FROM "TransactionEntities"
    WHERE "emiId" IN ('${emi_ids.join("','")}') AND "status" = 'COMPLETED' AND "type" != 'REFUND'`;
    const trans_list = await this.pg.query(raw_query);
    const trans_map = {};
    trans_list.map((el) => {
      el.completionDate = this.dateService.getGlobalDate(el.completionDate);
      if (!trans_map[el.emiId]) {
        trans_map[el.emiId] = [];
      }

      trans_map[el.emiId].push(el);
    });

    return { finalized_months, target_disbursed_loans, emi_list, trans_map };
  }

  async creteReport(reqData) {
    return await this.pg.create(ReportHistoryEntity, reqData);
  }

  async getReport(options) {
    return await this.pg.findOne(ReportHistoryEntity, options);
  }
}
