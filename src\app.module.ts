// Imports
import { Module } from '@nestjs/common';
import { AppService } from './app.service';
import { AppController } from './app.controller';
import { PgModule } from './database/pg/pg.module';
import { UtilsModule } from './utils/utils.module';
import { AdminModule } from './admin/admin.module';
import { LoanModule } from './admin/loan/loan.module';
import { BankModule } from './admin/bank/bank.module';
import { UserModule } from './admin/user/user.module';
import { LeadModule } from './admin/lead/lead.module';
import { BureauModule } from './admin/bureau/bureau.module';
import { NeighbourModule } from './neighbours/neighbour.module';
import { ClickHouseModule } from './database/clickhouse/clickhouse.module';
import { ThirdPartyModule } from './thirdparty/thirdparty.module';
import { EligibilityModule } from './eligibility/eligibility.module';
import { CommunicationModule } from './admin/communication/communication.module';
import { RedisModule } from './database/redis/redis.module';
import { PaymentModule } from './payment/payment.module';
import { NotificationService } from './notification/notification.service';
import { CommonModule } from './common/common.module';

@Module({
  imports: [
    AdminModule,
    BankModule,
    BureauModule,
    ClickHouseModule,
    CommunicationModule,
    EligibilityModule,
    LeadModule,
    LoanModule,
    NeighbourModule,
    PaymentModule,
    PgModule,
    ThirdPartyModule,
    UserModule,
    UtilsModule,
    CommonModule,
    RedisModule,
  ],
  controllers: [AppController],
  providers: [AppService, NotificationService],
})
export class AppModule {}
