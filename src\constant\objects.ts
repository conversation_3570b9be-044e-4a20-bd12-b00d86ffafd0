export const ENUM_DATA = {
  ACTIVE_LOAN: 'Active',
};

export const EXPERIAN_GOOD_COMPANIES = [
  'PLEBGRAPH FINANCE PVT LTD',
  'EARLYSALARY SERVICES PVT LTD',
  '<PERSON><PERSON><PERSON><PERSON>VE FINANCIAL SERVICES LTD',
  'CITRA FINANCIALS PRIVATE LIMITED',
  'NIYOGIN FINTECH LIMITED',
  'VAIBHAV VYAPAAR PVT LTD',
  'MAS FINANCIAL SERVICES LTD',
  'VIVRITI CAPITAL LIMITED',
  'HDB FINANCIAL SERVICES LIMITED',
  'UNIFINZ CAPITAL INDIA LIMITED',
  'RESPO FINANCIAL CAPITAL PRIVATE LIMITED',
  'INCRED FINANCIAL SERVICES LIMITED',
  'RULOANS FINANCIAL SERVICES PRIVATE LIMITED',
  'VIVIFI INDIA FINANCE (P) LTD',
  'FDPL FINANCE PRIVATE LIMITED',
  'TAPSTART CAPITAL PVT LTD',
  'GIRDHAR FINLEASE PVT LTD',
  'ICI<PERSON> BANK',
  'AKARA CAPITAL ADVISORS PRIVATE LIMITED',
  'UPMOVE CAPITAL PVT LTD',
  'CAPRI GLOBAL CAPITAL LIMITED',
  'KASAR CREDIT AND CAPITAL PRIVATE LIMITED',
  'INNOFIN SOLUTIONS PVT LTD',
  'ADITYA BIRLA FINANCE LTD',
  'HDFC BANK LTD',
  'FEDERAL BANK',
  'BABA LEASE AND INVESTMENT PVT LTD',
  'MAHASHAKTI FINANCIERS LIMITED',
  'KISETSU SAISON FINANCE (INDIA) PRIVATE LIMITED',
  'ARNOLD HOLDINGS LTD',
  'SOLOMON CAPITAL PRIVATE LIMITED',
  'SMFG INDIA CREDIT COMPANY LIMITED',
  'R K BANSAL FINANCE PRIVATE LIMITED',
  'POONAWALLA FINCORP LIMITED',
  'OXYZO FINANCIAL SERVICES PVT LTD',
  'SALORA CAPITAL LIMITED',
  'NAVI FINSERV LIMITED',
  'ZED LEAFIN PVT.LTD',
  'PAYU FINANCE INDIA PVT LTD',
  'SI CREVA CAPITAL',
  'ACHIIEVERS FINANCE INDIA PRIVATE LIMITED',
  'BHAWANA SECURITIES AND FINANCIAL SERVICES LTD',
  'STATE BANK OF INDIA',
  'FINC FRIENDS PRIVATE LIMITED',
  'GRAND TOTAL',
  'SAYYAM INVESTMETS PVT LTD',
  'KOTAK MAHINDRA BANK LIMITED',
  'NOT_DEFINED',
  'AXIS BANK',
  'SAMPATI SECURITIES LIMITED',
  'NORTHERN ARC CAPITAL LIMITED',
  'TRUE CREDITS PRIVATE LIMITED',
  'MPOKKET FINANCIAL SERVICES PVT LTD',
  'KRAZYBEE SERVICES PRIVATE LIMITED',
  'MUTHOOT FINANCE LIMITED',
  'IDFC FIRST BANK LIMITED',
  'BAJAJ FINANCE LIMITED',
  'MAHAVIRA FINLEASE LIMITED',
  'BRANCH INTERNATIONAL FINANCIAL SERVICES PVT LTD',
];

export const DATA_CODES_CLICKHOUSE_TABLE = {
  emi_details: 'emi_details',
  experian_soft_eligible: 'experian_soft_eligible',
  payment_details: 'payment_details',
};

export const platforms = {
  '0': 'Android',
  '1': 'IOS',
  '2': 'Web App',
};

export const leadStatusValue = {
  '0': 'ALL',
  '1': 'UNDER_VERIFICATION',
  '2': 'PENDING',
  '3': 'INITIATED',
};

export const NUMBERS = {
  ONE_MINUTE_IN_SECONDS: 60,
  HALF_AN_HOUR_IN_SECONDS: 30 * 60,
  FIVE_MINUTES_IN_SECONDS: 5 * 60,
  TEN_MINUTES_IN_SECONDS: 10 * 60,
  ONE_HOURS_IN_SECONDS: 1 * 60 * 60,
  THREE_HOURS_IN_SECONDS: 3 * 60 * 60,
  FIVE_HOURS_IN_SECONDS: 5 * 60 * 60,
  ONE_DAY_IN_SECONDS: 24 * 60 * 60,
  THREE_DAYS_IN_SECONDS: 3 * 24 * 60 * 60,
  FIVE_DAYS_IN_SECONDS: 5 * 24 * 60 * 60,
  SEVEN_DAYS_IN_SECONDS: 7 * 24 * 60 * 60,
  THIRTY_DAYS_IN_SECONDS: 30 * 24 * 60 * 60,
};
export const stageMapping = {
  '1': 'PHONE_VERIFICATION',
  '2': 'BASIC_DETAILS',
  '3': 'SELFIE',
  '4': 'NOT_ELIGIBLE',
  '5': 'PIN',
  '6': 'AADHAAR',
  '7': 'EMPLOYMENT',
  '8': 'BANKING',
  '9': 'RESIDENCE',
  '10': 'LOAN_ACCEPT',
  '11': 'CONTACT',
  '12': 'PAN',
  '13': 'FINAL_VERIFICATION',
  '14': 'MANDATE',
  '15': 'ESIGN',
  '16': 'DISBURSEMENT',
  // '17': 'REPAYMENT',
  '18': 'DEFAULTER',
  '19': 'REAPPLY',
  '20': 'NO_ROUTE',
  '21': 'EXPRESS-REAPPLY',
};

export const uiStageMapping = {
  '1': 'Phone Verification',
  '2': 'Basic Details',
  '5': 'Pin',
  '6': 'Aadhaar',
  '3': 'Selfie',
  '7': 'Employment',
  '8': 'Banking',
  '12': 'Pan',
  '13': 'Final Verification',
  '10': 'Loan Accept',
  '14': 'E-Mandate',
  '15': 'E-sign',
  '16': 'Disbursement',
  '4': 'Not Eligible',
  // '17': 'Repayment',
  // '18': 'Defaulter',
  // '19': 'Reapply',
  // '20': 'No Route',
  // '21': 'Express Reapply',
};

export const CLOUD_FOLDER = {
  reports: 'REPORTS',
  default: 'DEFAULT',
};

export const kMimeTypes = {
  json: 'application/json',
  formdata: 'multipart/form-data',
  csv: 'text/csv',
  doc: 'application/msword',
  docs: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  html: 'text/html',
  css: 'text/css',
  jpg: 'image/jpeg',
  JPG: 'image/jpeg',
  jpeg: 'image/jpeg',
  JPEG: 'image/jpeg',
  pdf: 'application/pdf',
  PDF: 'application/pdf',
  png: 'image/png',
  PNG: 'image/png',
  gif: 'image/gif',
  GIF: 'image/gif',
  svg: 'image/svg+xml',
  SVG: 'image/svg+xml',
  txt: 'text/plain',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
};

export const kFonts = {
  big: {
    font: {
      size: 16,
    },
    alignment: {
      wrapText: true,
    },
  },
  small: {
    font: {
      size: 12,
    },
    alignment: {
      wrapText: true,
    },
  },
};

export const kEMIEnums = {
  ONTIME: 'ON_TIME',
  PREPAID: 'PRE_PAID',
  DEFAULTER: 'DEFAULTER',
  DELAYED: 'DELAYED',
  UPCOMING: 'UPCOMING',
};

export const kServerStr = {
  nestJsInit: 'Attempting to start the NestJs server...',
  runningPort: 'Server is running on port -> ',
};

export const kRouteEnums = { SELECT_LOAN_AMOUNT: 'selectLoanAmountRoute' };

export const kUserStageEnums = {
  PHONE_VERIFICATION: 'PHONE VERIFICATION',
  BASIC_DETAILS: 'BASIC DETAILS',
  SELFIE: 'SELFIE',
  PIN: 'PIN',
  AADHAAR: 'AADHAAR',
  EMPLOYMENT: 'EMPLOYMENT DETAILS',
  EXPRESS_REAPPLY: 'EXPRESS REAPPLY',
  BANKING: 'BANK VERIFICATION',
  FINAL_VERIFICATION: 'FINAL VERIFICATION',
  LOAN_ACCEPT: 'LOAN_ACCEPT',
  MANDATE: 'MANDATE',
  ESIGN: 'ESIGN',
  ON_HOLD: 'ON HOLD',
  NO_ROUTE: 'NO_ROUTE',
  NOT_ELIGIBLE: 'NOT ELIGIBLE',
  RESIDENCE: 'RESIDENCE',
  CONTACT: 'CONTACT',
  PAN: 'PAN',
  DISBURSEMENT: 'DISBURSEMENT',
  DEFAULTER: 'DEFAULTER',
  REAPPLY: 'REAPPLY',
};

export const kUserCallbackPreference = {
  '1': '10AM to 12PM',
  '2': '2PM to 4PM',
  '3': '4PM to 6PM',
};

export const kCallbackStatus = {
  '0': 'Not Called',
  '1': 'Called',
};

export const kSupportAdminIds = [109, 155, 197, 314];

export const CLOUD_FOLDER_PATH = {
  eSign: 'ESIGN',
  agreement: 'AGREEMENT',
  reports: 'REPORTS',
  selfie: 'SELFIE',
  app: 'APP',
  web: 'WEB',
  dashboard: 'DASHBOARD',
  default: 'DEFAULT',
  legal: 'LEGAL',
  tally: 'TALLY',
  leadTracking: 'LEAD_TRACKING',
  razorpay: 'RAZORPAY',
  misc: 'MISC',
  logHistory: 'LOG_HISTORY',
  webRegister: 'WEB_REGISTER_USERS',
  duplicateTransactions: 'DUPLICATE_TRANSACTION',
  cibil: 'CIBIL',
  compliance: 'COMPLIANCE',
  metrics: 'METRICS',
  notification: 'NOTIFICATION',
  transactions: 'TRANSACTIONS',
  invoice: 'INVOICE',
  oneMoney: 'ONE_MONEY',
  compressedFiles: 'CPMPRESSED_FILES',
  banner: 'BANNER_IMAGES',
  capturedFrames: 'CAPTURED_FRAMES',
  finvu: 'FINVU',
  nonRegister: 'NON_REGISTER_USERS',
  noc: 'NOC',
  aadhaar: 'AADHAAR',
  Backend_static_stuff: 'BACKEND_STATIC_STUFF',
  whatsApp: 'WHATSAPP',
  APP_BANK_STATEMENTS: 'APP_BANK_STATEMENTS',
  validateEligibility: 'VALIDATE_ELIGIBILITY',
  cams: 'CAMS',
  agentCallRecordings: 'AGENT_CALL_RECORDINGS',
  camsTransactions: 'CAMS_TRANSACTIONS',
  miscUploadFile: 'MISC_UPLOAD_FILE',
  convertBase64ToPdf: 'CONVERT_BASE64_TO_PDF',
  syncData: 'SYNC_DATA',
  prepareWebviewData: 'PREPARE_WEBVIEW_DATA',
  uploadAdditionalStatement: 'UPLOAD_ADDITIONAL_STATEMENT',
  submitNetbankingTrigger: 'SUBMIT_NETBANKING_TRIGGER',
  sendNotificationToUser: 'SEND_NOTIFICATION_TO_USER',
  numberToUserIds: 'NUMBER_TO_USERIDS',
  decryptedPhoneNumbers: 'DECRYPTED_PHONE_NUMBERS',
  refreshuserstage: 'REFRESH_USER_STAGE',
};

// User Statuses
export const finalStatuses = {
  '0': '-',
  '1': 'Pending',
  '2': 'Under Verfication',
};
