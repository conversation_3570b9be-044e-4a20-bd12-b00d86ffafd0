// Imports
import { Injectable } from '@nestjs/common';
import { ReportQueryService } from './report.query';
import { TransactionEntity } from 'src/database/pg/entities/transaction.entity';
import { DateService } from 'src/utils/date.service';
import {
  raiseNotFound,
  raiseParamInvalid,
  raiseParamMissing,
} from 'src/config/error';
import { kSystem, SYSTEM_ADMIN_ID } from 'src/constant/global';
import { decryptPhone } from 'src/utils/crypt';
import { FileService } from 'src/utils/file.service';
import { NumberService } from 'src/utils/number.service';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { crmActivity } from 'src/database/pg/entities/crmActivity';
import { ReportHistoryEntity } from 'src/database/pg/entities/reportHistory.entity';
import { PAGE, PAGE_LIMIT } from 'src/constant/global';
import { exotelCallbackWaitlistEntity } from 'src/database/pg/entities/exotelCallback.entity';
import { kCallbackStatus, kUserCallbackPreference } from 'src/constant/objects';
import { kActiveLoan } from 'src/constant/strings';
import { CommonService } from 'src/common/common.services';
import { PgService } from 'src/database/pg/pg.service';
import { ExotelQuery } from 'src/thirdParty/exotel/exotel.query';
import { Op } from 'sequelize';

@Injectable()
export class ReportService {
  constructor(
    private readonly query: ReportQueryService,
    private readonly dateService: DateService,
    private readonly fileService: FileService,
    private readonly numService: NumberService,
    private readonly commonService: CommonService,
    private readonly pg: PgService,
    private readonly exotelQuery: ExotelQuery,
  ) {}

  async collectionEfficiency(reqData) {
    const data = await this.query.dataForCollectionEfficiency(reqData);

    let expected_same_month_due_principal = 0;
    let expected_same_month_due_interest = 0;
    let paid_same_month_due_principal = 0;

    for (let index = 0; index < data.currentMonthEmiList.length; index++) {
      const emiData = data.currentMonthEmiList[index];
      const isDelayed = emiData.payment_due_status == '1';
      const isFullPay = emiData.pay_type == 'FULLPAY';
      let paid_principal = 0;
      let paid_interest = 0;

      expected_same_month_due_principal += emiData.principalCovered;

      if (isDelayed || !isFullPay) {
        expected_same_month_due_interest += emiData.interestCalculate ?? 0;

        // Delayed and full paid
        if (isFullPay) {
          paid_principal += emiData.principalCovered;
          paid_interest += emiData.fullPayInterest;
        }
      } else if (isFullPay) {
        expected_same_month_due_interest += emiData.fullPayInterest ?? 0;
        paid_principal += emiData.principalCovered;
        paid_interest += emiData.fullPayInterest;
      }

      const paymentData: TransactionEntity[] =
        data.currentMonthPaymentData[emiData.id] ?? [];

      paymentData.forEach((el) => {
        paid_principal += el.principalAmount ?? 0;
        paid_interest += el.interestAmount ?? 0;
      });

      paid_same_month_due_principal += paid_principal;
    }

    return {
      expected_same_month_due_principal,
      paid_same_month_due_principal,

      expected_same_month_due_interest,

      carry_forward_od_principal_paid: data.carry_forward_od_principal_paid,
      carry_forward_od_interest_paid: data.carry_forward_od_interest_paid,
    };
  }

  async csePerformanceReport(reqData) {
    const startDate = reqData?.startDate;
    const endDate = reqData?.endDate ?? startDate;
    //loanType 0 for Repeated Loans, 1 for New Loans, -1 for All Loans
    const loanType = reqData?.loanType ?? -1;
    if (![-1, 0, 1].includes(loanType)) raiseParamInvalid('loanType');
    if (!startDate) raiseParamMissing('startDate');
    if (!endDate) raiseParamMissing('endDate');
    const needRawData = reqData?.needRawData === true;
    const needDownload = reqData?.needDownload === true;
    const isDailyReport = startDate == endDate ? true : false;
    const fromCron = reqData?.fromCron === true;

    const { minRange, maxRange } = this.dateService.istDateRange(
      startDate,
      endDate,
    );
    let finalResult = { count: 0, rows: [] };

    if (
      !fromCron &&
      isDailyReport &&
      this.dateService.getGlobalDate(startDate).toJSON() !=
        this.dateService.getGlobalDate(new Date()).toJSON()
    ) {
      const reportData: ReportHistoryEntity = await this.query.getReport({
        attributes: ['id', 'downloadUrl'],
        where: {
          fromDate: this.dateService.getGlobalDate(startDate).toJSON(),
          toDate: null,
          reportName: 'CSE Performance Report',
          status: '1',
          extraparms: { needRawData, loanType },
        },
        order: [['id', 'DESC']],
      });
      if (reportData && reportData?.downloadUrl) {
        if (needDownload) return { fileURL: reportData?.downloadUrl };
        else {
          const localFilePath = await this.fileService.fileUrlToFile(
            reportData?.downloadUrl,
          );
          const resultArr: any =
            await this.fileService.excelToArray(localFilePath);
          finalResult.count = resultArr?.length;
          finalResult.rows = resultArr;
          this.fileService.removeFile(localFilePath);
          return finalResult;
        }
      }
    }

    const crmMinRange = new Date(minRange);
    crmMinRange.setDate(crmMinRange.getDate() - 7);
    const allDataForReport = await this.query.dataForCsePerformanceReport({
      minRange,
      crmMinRange,
      maxRange,
      needRawData,
      loanType,
      isDailyReport,
    });

    if (needRawData)
      finalResult = await this.makeCsePerformanceRawData(allDataForReport, {
        minRange,
        maxRange,
      });
    else
      finalResult = await this.makeCsePerformanceSummaryData(allDataForReport, {
        minRange,
        maxRange,
      });
    if (finalResult?.count == 0) raiseNotFound();

    if (!needDownload) return finalResult;
    const rawExcelData = {
      sheets: ['CSE Performance Report'],
      data: [finalResult.rows],
      sheetName: 'CSE Performance Report.xlsx',
    };
    const fileURL = await this.fileService.objectToExcelURL(rawExcelData, true);

    if (fromCron) {
      const rawData = {
        adminId: SYSTEM_ADMIN_ID,
        fromDate: this.dateService.getGlobalDate(startDate).toJSON(),
        toDate: null,
        downloadUrl: fileURL,
        reportName: 'CSE Performance Report',
        status: '1',
        apiUrl: 'report/csePerformanceReport',
        extraparms: { needRawData, loanType },
      };
      await this.query.creteReport(rawData);
    }
    return { fileURL };
  }

  private async makeCsePerformanceSummaryData(allData, dateRange) {
    const { adminData: cseAdminData, loanData, crmData } = allData;
    const { maxRange, minRange } = dateRange;
    const formattedCrmData = {};
    //format crmData Data
    crmData.forEach((crm) => {
      formattedCrmData[crm.loanId] = { ...crm };
    });

    //format Admin Data
    const formattedAdminData = {
      [SYSTEM_ADMIN_ID]: {
        fullName: kSystem,
        isActive: 1,
        inProgress: 0,
        declined: 0,
        disbursed: 0,
        loanAmount: 0,
      },
    };
    cseAdminData.forEach((admin) => {
      formattedAdminData[admin.id] = {
        fullName: admin.fullName,
        isActive: admin?.isActive == '1' ? 1 : 0,
        inProgress: 0,
        declined: 0,
        disbursed: 0,
        loanAmount: 0,
      };
    });

    const maxTime: number = maxRange.getTime();
    const minTime: number = minRange.getTime();
    let id: number,
      loanStatus: string,
      loanRejectDateTime: number,
      loanDisbursementDateTime: number,
      loanCreatedDateTime: number,
      crmDateTime: number,
      crm: crmActivity;

    //prepare formattedAdminData
    for (const loan of loanData) {
      loan.loanRejectDate = loan.loanRejectDateTime
        ? this.dateService.dateToJsonStr(loan.loanRejectDateTime, 'YYYY-MM-DD')
        : null;
      loan.loanDisbursementDate = loan.loanDisbursementDateTime
        ? this.dateService.dateToJsonStr(
            loan.loanDisbursementDateTime,
            'YYYY-MM-DD',
          )
        : null;

      id = loan?.id;
      loanStatus = loan?.loanStatus;
      loanRejectDateTime = loan?.loanRejectDateTime?.getTime();
      loanDisbursementDateTime = loan?.loanDisbursementDateTime?.getTime();
      loanCreatedDateTime = loan?.createdAt?.getTime();
      crm = formattedCrmData[id];
      crmDateTime = crm?.createdAt?.getTime();
      if (
        !id ||
        !loanStatus ||
        !loanCreatedDateTime ||
        (['Rejected', 'Active', 'Complete']?.includes(loanStatus) &&
          ((!loanRejectDateTime && !loanDisbursementDateTime) ||
            loanCreatedDateTime >=
              (loanRejectDateTime || loanDisbursementDateTime)))
      )
        continue;

      if (crmDateTime && crmDateTime >= loanCreatedDateTime) {
        if (loanStatus == 'Accepted' || loanStatus == 'InProcess') {
          if (minTime <= crmDateTime && crmDateTime <= maxTime)
            formattedAdminData[crm.adminId].inProgress++;
          else formattedAdminData[SYSTEM_ADMIN_ID].inProgress++;
        } else if (loanStatus == 'Rejected') {
          if (
            crmDateTime <= loanRejectDateTime &&
            loanRejectDateTime <= maxTime
          )
            formattedAdminData[crm.adminId].declined++;
          else if (crmDateTime <= loanRejectDateTime)
            formattedAdminData[crm.adminId].inProgress++;
          else formattedAdminData[SYSTEM_ADMIN_ID].declined++;
        } else {
          if (
            crmDateTime <= loanDisbursementDateTime &&
            loanDisbursementDateTime <= maxTime
          ) {
            formattedAdminData[crm.adminId].disbursed++;
            formattedAdminData[crm.adminId].loanAmount += +(
              loan?.netApprovedAmount ?? '0'
            );
          } else if (crmDateTime <= loanDisbursementDateTime)
            formattedAdminData[crm.adminId].inProgress++;
          else {
            formattedAdminData[SYSTEM_ADMIN_ID].disbursed++;
            formattedAdminData[SYSTEM_ADMIN_ID].loanAmount += +(
              loan?.netApprovedAmount ?? '0'
            );
          }
        }
      } else {
        if (loanStatus == 'Accepted' || loanStatus == 'InProcess')
          formattedAdminData[SYSTEM_ADMIN_ID].inProgress++;
        else if (loanStatus == 'Rejected') {
          if (loanRejectDateTime <= maxTime)
            formattedAdminData[SYSTEM_ADMIN_ID].declined++;
          else formattedAdminData[SYSTEM_ADMIN_ID].inProgress++;
        } else {
          if (loanDisbursementDateTime <= maxTime) {
            formattedAdminData[SYSTEM_ADMIN_ID].disbursed++;
            formattedAdminData[SYSTEM_ADMIN_ID].loanAmount += +(
              loan?.netApprovedAmount ?? '0'
            );
          } else formattedAdminData[SYSTEM_ADMIN_ID].inProgress++;
        }
      }
    }

    //prepare final result
    const resultArr = [];
    for (const adminId in formattedAdminData) {
      const inProgressLead = formattedAdminData[adminId]?.inProgress;
      const declinedLead = formattedAdminData[adminId]?.declined;
      const disbursedLead = formattedAdminData[adminId]?.disbursed;
      const totalLeads = inProgressLead + declinedLead + disbursedLead;
      const totalAmount = formattedAdminData[adminId].loanAmount;
      const tempObj = {
        'Full Name': formattedAdminData[adminId]?.fullName,
        'Total Leads': totalLeads,
        'In Progress': inProgressLead,
        Declined: declinedLead,
        Disbursed: disbursedLead,
        Amount: totalAmount,
        Conversion: '0',
      };
      const conversion = totalLeads > 0 ? disbursedLead / totalLeads : 0;
      tempObj.Conversion = (conversion * 100).toFixed(2);
      if (formattedAdminData[adminId]?.isActive || totalLeads > 0)
        resultArr.push(tempObj);
    }
    //sort by fullName
    resultArr.sort((a, b) => a['Full Name'].localeCompare(b['Full Name']));
    return { count: resultArr.length, rows: resultArr };
  }

  private async makeCsePerformanceRawData(allData, dateRange) {
    const { adminData, loanData, crmData, masterData } = allData;
    const { maxRange, minRange } = dateRange;
    const formattedCrmData = {};

    //format crmData Data
    crmData.forEach((crm) => {
      crm.createdDate = this.dateService.dateToJsonStr(
        new Date(crm.createdDate),
      );
      formattedCrmData[crm.loanId] = { ...crm };
    });

    //format Admin Data
    const formattedAdminData = {};
    adminData.forEach((admin, idx) => {
      formattedAdminData[admin.id] = admin.fullName;
      if (idx == adminData.length - 1)
        formattedAdminData[SYSTEM_ADMIN_ID] = kSystem;
    });

    //format cse assign Data
    const formattedCSEAssignData = {};
    masterData.forEach((ele) => {
      formattedCSEAssignData[ele.loanId] =
        formattedAdminData[ele.assignedCSE] ?? kSystem;
    });

    const maxTime: number = maxRange.getTime();
    const minTime: number = minRange.getTime();
    let id: number,
      loanStatus: string,
      loanRejectDateTime: number,
      loanDisbursementDateTime: number,
      loanCreatedDateTime: number,
      crmDateTime: number,
      crm;

    //prepare resultArr
    const resultArr = [];
    for (const loan of loanData) {
      loan.loanRejectDate = loan.loanRejectDateTime
        ? this.dateService.dateToJsonStr(loan.loanRejectDateTime)
        : null;
      loan.loanDisbursementDate = loan.loanDisbursementDateTime
        ? this.dateService.dateToJsonStr(loan.loanDisbursementDateTime)
        : null;

      id = loan?.id;
      loanStatus = loan?.loanStatus;
      loanRejectDateTime = loan?.loanRejectDateTime?.getTime();
      loanDisbursementDateTime = loan?.loanDisbursementDateTime?.getTime();
      loanCreatedDateTime = loan?.createdAt?.getTime();
      crm = formattedCrmData[id];
      crmDateTime = crm?.createdAt?.getTime();
      if (
        !id ||
        !loanStatus ||
        !loanCreatedDateTime ||
        (['Rejected', 'Active', 'Complete']?.includes(loanStatus) &&
          ((!loanRejectDateTime && !loanDisbursementDateTime) ||
            loanCreatedDateTime >=
              (loanRejectDateTime || loanDisbursementDateTime)))
      )
        continue;

      const tempObj = {
        'Loan Id': '-',
        'User Id': '-',
        'User Name': '-',
        'Mobile Number': '-',
        'Completed Loan': 0,
        'CRM Date': '-',
        'CRM By': kSystem,
        'Rejected By': '-',
        'Rejected Date': '-',
        'Loan Amount': '-',
        'Loan Approved By': '-',
        'Disbursement Date': '-',
        'Assign CSE': formattedCSEAssignData[id] ?? kSystem,
      };
      tempObj['Loan Id'] = id?.toString();
      tempObj['User Id'] = loan?.userId ?? '-';
      tempObj['User Name'] = loan?.fullName ?? '-';
      tempObj['Mobile Number'] = loan?.phone ? decryptPhone(loan?.phone) : '-';
      tempObj['Completed Loan'] = loan?.completedLoan ?? 0;

      if (crmDateTime && crmDateTime >= loanCreatedDateTime) {
        if (loanStatus == 'Accepted' || loanStatus == 'InProcess') {
          if (minTime <= crmDateTime && crmDateTime <= maxTime) {
            tempObj['CRM Date'] = crm.createdDate;
            tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
          } else tempObj['CRM By'] = kSystem;
        } else if (loanStatus == 'Rejected') {
          if (
            crmDateTime <= loanRejectDateTime &&
            loanRejectDateTime <= maxTime
          ) {
            tempObj['CRM Date'] = crm.createdDate;
            tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
            tempObj['Rejected By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Rejected Date'] = loan.loanRejectDate;
          } else if (crmDateTime <= loanRejectDateTime) {
            tempObj['CRM Date'] = crm.createdDate;
            tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
          } else {
            tempObj['CRM By'] = kSystem;
            tempObj['Rejected By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Rejected Date'] = loan.loanRejectDate;
          }
        } else {
          if (
            crmDateTime <= loanDisbursementDateTime &&
            loanDisbursementDateTime <= maxTime
          ) {
            tempObj['CRM Date'] = crm.createdDate;
            tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
            tempObj['Loan Amount'] = loan?.netApprovedAmount;
            tempObj['Loan Approved By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Disbursement Date'] = loan.loanDisbursementDate;
          } else if (crmDateTime <= loanDisbursementDateTime) {
            tempObj['CRM Date'] = crm.createdDate;
            tempObj['CRM By'] = formattedAdminData[crm.adminId] ?? kSystem;
          } else {
            tempObj['CRM By'] = kSystem;
            tempObj['Loan Amount'] = loan?.netApprovedAmount;
            tempObj['Loan Approved By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Disbursement Date'] = loan.loanDisbursementDate;
          }
        }
      } else {
        if (loanStatus == 'Accepted' || loanStatus == 'InProcess')
          tempObj['CRM By'] = kSystem;
        else if (loanStatus == 'Rejected') {
          tempObj['CRM By'] = kSystem;
          if (loanRejectDateTime <= maxTime) {
            tempObj['Rejected By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Rejected Date'] = loan.loanRejectDate;
          }
        } else {
          tempObj['CRM By'] = kSystem;
          if (loanDisbursementDateTime <= maxTime) {
            tempObj['Loan Amount'] = loan?.netApprovedAmount;
            tempObj['Loan Approved By'] =
              formattedAdminData[loan.manualVerificationAcceptId] ?? kSystem;
            tempObj['Disbursement Date'] = loan.loanDisbursementDate;
          }
        }
      }

      resultArr.push(tempObj);
    }

    //sort resultArr
    resultArr.sort((a, b) => {
      let date1: string | number = '-';
      if (a['CRM Date'] != '-') date1 = a['CRM Date'];
      else if (a['Disbursement Date'] != '-') date1 = a['Disbursement Date'];
      else if (a['Rejected Date'] != '-') date1 = a['Rejected Date'];
      if (date1 != '-')
        date1 = this.dateService
          .strToDate((date1 as string).replaceAll('-', ''), 'DDMMYYYY')
          .getTime();

      let date2: string | number = '-';
      if (b['CRM Date'] != '-') date2 = b['CRM Date'];
      else if (b['Disbursement Date'] != '-') date2 = b['Disbursement Date'];
      else if (b['Rejected Date'] != '-') date2 = b['Rejected Date'];
      if (date2 != '-')
        date2 = this.dateService
          .strToDate((date2 as string).replaceAll('-', ''), 'DDMMYYYY')
          .getTime();

      if (date1 == '-' && date2 == '-') return 0;
      if (date1 == '-') return 1;
      if (date2 == '-') return -1;
      return date1 - date2;
    });
    return { count: resultArr.length, rows: resultArr };
  }

  async aadhaarStateCollection(reqData) {
    let startDate = reqData?.startDate;
    let endDate = reqData?.endDate;
    if (!startDate) raiseParamMissing('startDate');
    if (!endDate) raiseParamMissing('endDate');
    startDate = this.dateService.getGlobalDate(startDate)?.toJSON();
    endDate = this.dateService.getGlobalDate(endDate)?.toJSON();
    const needDownload = reqData?.needDownload === true;
    const due_loan_data = await this.query.dataForOverallAadhaarStateCollection(
      { startDate, endDate },
    );

    const total_loan_data =
      await this.query.dataForTotalLoansForAadhaarStateCollection({
        startDate,
        endDate,
      });

    // Mapping -> "total_loan_data" into "due_loan_data"
    const finalizedList = [];
    for (let index = 0; index < due_loan_data.length; index++) {
      const value = due_loan_data[index];
      const aadhaarState = value['Aadhaar State'];

      const mappedData = total_loan_data.find(
        (el) => el.aadhaarState == aadhaarState,
      );
      if (mappedData) {
        finalizedList.push({
          'Aadhaar State': value['Aadhaar State'],
          'Total Loans': +mappedData['Total Loans'],
          'Total Due Loans': this.numService.withCommas(
            value['Total Due Loans'],
          ),
          'Total Paid Loans': this.numService.withCommas(
            +mappedData['Paid Loans'],
          ),
          'Expected EMI Amount': this.numService.withCommas(
            value['Expected EMI Amount'],
            true,
          ),
          'PAID EMI Amount': this.numService.withCommas(
            value['PAID EMI Amount'],
            true,
          ),
          'Overall Collection Efficiency (%)':
            value['Overall Collection Efficiency (%)'],
          'Collection Efficiency (0 DPD)':
            value['Collection Efficiency (0 DPD)'],
          'Collection Efficiency (1–30 DPD)':
            value['Collection Efficiency (1–30 DPD)'],
          'Collection Efficiency (31–90 DPD)':
            value['Collection Efficiency (31–90 DPD)'],
          'Collection Efficiency (90+ DPD)':
            value['Collection Efficiency (90+ DPD)'],
        });
      }
    }
    if (finalizedList?.length == 0) raiseNotFound();
    if (!needDownload)
      return { count: finalizedList.length, rows: finalizedList };
    const rawExcelData = {
      sheets: ['State Collection Efficiency'],
      data: [finalizedList],
      sheetName: 'State Collection Efficiency.xlsx',
    };
    const fileURL = await this.fileService.objectToExcelURL(rawExcelData, true);
    return { fileURL };
  }

  async vintageReport(reqData) {
    const max_dpd = reqData.max_dpd;
    if (!max_dpd) {
      raiseParamMissing('max_dpd');
    }
    const download_data = reqData.download_data ?? {};
    const is_download =
      download_data.download == 'true' || download_data.download == true;
    const download_core_month = download_data.core_month;
    if (!download_core_month && is_download) {
      raiseParamMissing('core_month');
    }
    const download_sub_month = download_data.sub_month;
    if (!download_sub_month && is_download) {
      raiseParamMissing('sub_month');
    }

    const target_data = await this.query.dataForVintageReport(reqData);

    const finalized_months = target_data.finalized_months ?? [];
    const total_loans = target_data.target_disbursed_loans ?? [];
    const loan_map = {};
    total_loans.forEach((el) => {
      loan_map[el.id] = el;
    });
    const total_emis = target_data.emi_list ?? [];
    const payments_map = target_data.trans_map;

    const finalized_data = {};
    const unique_emi_data = {};
    const today_global_date = this.dateService.getTodayGlobalDate();
    const today_global_time = today_global_date.getTime();

    const raw_data = [];

    for (let i = 0; i < finalized_months.length; i++) {
      const target_month = finalized_months[i];
      console.log({ target_month });

      finalized_data[target_month] = {};

      const target_loans = total_loans.filter(
        (el) => el.disb_month == target_month,
      );
      let total_disb_amt = 0;
      target_loans.forEach((el) => {
        total_disb_amt += +el.approved_amt;
      });
      finalized_data[target_month]['Disbursement Amt(Cr)'] = total_disb_amt;

      const date_range =
        this.dateService.getDateRangeFromMonthYear(target_month);
      const core_start_date = new Date(date_range.start_date);
      const core_start_month_year = `${core_start_date.getFullYear()}-${core_start_date.getMonth()}`;
      const target_emis = total_emis;

      for (let j = 0; j < finalized_months.length; j++) {
        const sub_target_month = finalized_months[j];

        finalized_data[target_month][sub_target_month] = 0;
        const sub_date_range =
          this.dateService.getDateRangeFromMonthYear(sub_target_month);
        const sub_start_date = new Date(sub_date_range.start_date);
        const sub_start_time = sub_start_date.getTime();
        const sub_end_date = new Date(sub_date_range.end_date);
        const sub_end_time = sub_end_date.getTime();

        // Mapping month wise outstanding
        for (let k = 0; k < target_emis.length; k++) {
          const emiData = target_emis[k];
          const emi_date = this.dateService.getGlobalDate(emiData.emi_date);
          const emi_time = emi_date.getTime();

          // Future EMIs
          if (emi_time > today_global_time) {
            continue;
          }
          if (sub_start_time > emi_time && sub_start_time > today_global_time) {
            continue;
          }

          const loan_data = loan_map[emiData.loanId];
          const loan_disbursement_date = new Date(
            loan_data.loan_disbursement_date,
          );
          const disbursement_time = loan_disbursement_date.getTime();
          if (disbursement_time > sub_start_time) {
            continue;
          }
          if (sub_end_time < emi_date.getTime()) {
            continue;
          }
          const disb_month_year = `${loan_disbursement_date.getFullYear()}-${loan_disbursement_date.getMonth()}`;
          if (disb_month_year != core_start_month_year) {
            continue;
          }

          const payment_done_date = emiData.payment_done_date;

          let dpd_days = 0;
          // EMI is paid
          if (payment_done_date) {
            const emi_paid_date = new Date(payment_done_date);
            if (
              emi_paid_date.getTime() >=
              new Date(sub_date_range.start_date).getTime()
            ) {
              if (emi_date.getTime() < emi_paid_date.getTime()) {
                if (emi_paid_date.getTime() > sub_end_time) {
                  dpd_days = this.dateService.difference(
                    sub_end_date,
                    emi_date,
                  );
                } else {
                  dpd_days = this.dateService.difference(
                    emi_paid_date,
                    emi_date,
                  );
                }
              }
            }
          } else {
            dpd_days = this.dateService.difference(sub_end_date, emi_date);
          }

          if (dpd_days > max_dpd) {
            const unpaid_principal = this.getUnpaidPrincipal(
              emiData.principalCovered,
              payments_map[emiData.id],
              sub_end_date,
            );
            if (unpaid_principal <= 0) {
              continue;
            }

            const calculated_emis = total_emis.filter(
              (el) => el.loanId == emiData.loanId && el.id >= emiData.id,
            );

            calculated_emis.forEach((el: EmiEntity) => {
              const unique_key = `${target_month}_${sub_target_month}_${el.id}`;
              if (!unique_emi_data[unique_key]) {
                const unpaid_principal = this.getUnpaidPrincipal(
                  el.principalCovered,
                  payments_map[el.id],
                  sub_end_date,
                );

                unique_emi_data[unique_key] = '-';
                finalized_data[target_month][sub_target_month] +=
                  unpaid_principal;

                if (
                  is_download &&
                  download_core_month == target_month &&
                  sub_target_month == download_sub_month
                ) {
                  raw_data.push({
                    emi_id: el.id,
                    loan_id: el.loanId,
                    unpaid_principal: unpaid_principal,
                  });
                }
              }
            });
          }
        }
      }
    }

    console.log('finalized_data', finalized_data);

    return {
      success: true,
      data: finalized_data,
      raw_data: is_download ? raw_data : undefined,
    };
  }

  private getUnpaidPrincipal(
    expected_principal: number,
    emi_payments: any[],
    as_on_date: Date,
  ) {
    if (!emi_payments) {
      emi_payments = [];
    }

    let paid_principal = 0;
    for (let index = 0; index < emi_payments.length; index++) {
      const payment_data = emi_payments[index];

      if (payment_data.completionDate.getTime() > as_on_date.getTime()) {
        continue;
      }

      paid_principal += payment_data.principalAmount ?? 0;
    }

    if (expected_principal <= paid_principal) {
      return 0;
    } else {
      return expected_principal - paid_principal;
    }
  }

  async getScheduledCallbackRecord(reqData) {
    // Vaidate and intialize params....
    const isCalled = reqData?.isCalled;
    const page = reqData?.page ?? PAGE;
    const offset = (page - 1) * PAGE_LIMIT;
    const callbackSlot = reqData?.callbackSlot;
    const endDate = new Date(reqData?.endDate ?? new Date());
    const startDate = new Date(reqData?.startDate ?? new Date());
    const dates = await this.dateService.utcDateRange(startDate, endDate);

    const paginate = { page, limit: PAGE_LIMIT };
    const filterBy = {
      attributes: [
        'userId',
        'loanId',
        'callFrom',
        'callStatus',
        'callStartAt',
        'assignedAdmin',
        'callbackPreference',
      ],
      where: {
        callStartAt: { [Op.gte]: dates.start, [Op.lt]: dates.end },
      },
      offset,
      order: [['callStartAt', 'DESC']],
    };

    if (isCalled) filterBy.where['callStatus'] = isCalled;
    if (callbackSlot) filterBy.where['callbackPreference'] = callbackSlot;

    const callbackRecord = await this.pg.findAndCountAll(
      exotelCallbackWaitlistEntity,
      paginate,
      filterBy,
    );

    const rows = callbackRecord?.rows;
    const userIds = [
      ...new Set(rows.map((row) => row?.userId).filter((id) => id != null)),
    ];
    const userDetails = await this.exotelQuery.getCompleteUsersDetails(userIds);

    for (let i = 0; i < rows?.length; i++) {
      let row = rows[i];

      let adminId;
      const userId = row?.userId;
      const userData = userDetails?.userData.find((user) => user?.id == userId);
      const masterData = userData?.masterData;
      const loanData = userDetails?.loanData.find(
        (loan) => loan.id == userData?.lastLoanId,
      );
      row.userId = userId ?? '-';
      row['Name'] = userData?.fullName ?? '-';
      row['Loan id'] = row?.loanId ?? '-';
      row['Called at'] = this.dateService.convertToReadableIST(
        row?.callStartAt,
      );
      row['Status'] = kCallbackStatus[row?.callStatus] ?? '-';

      row['Callback preference'] =
        kUserCallbackPreference[row?.callbackPreference] ?? '-';

      if (loanData?.loanStatus != kActiveLoan && masterData?.assignedCSE)
        adminId = masterData?.assignedCSE;
      else if (loanData?.loanStatus == kActiveLoan) {
        if (loanData?.followerId) {
          adminId = loanData?.followerId;
        } else adminId = masterData?.assignedCSE;
      }
      row['Assigned admin'] = (
        await this.commonService.getAdminData(adminId)
      )?.fullName;

      delete row.callbackPreference;
      delete row.callStatus;
      delete row.callStartAt;
      delete row.assignedAdmin;
      delete row.callFrom;
      delete row.loanId;
    }
    return callbackRecord;
  }
  // #region download report common function
  //#region
  async downloadReport(reqData, data) {
    const rawExcelData = {
      sheets: ['local-reports'],
      data: [data],
      sheetName: 'StageWiseLeadsData.xlsx',
      needFindTuneKey: false,
      reportStore: true,
      startDate: reqData.startDate,
      endDate: reqData.endDate,
    };
    const url: any = await this.fileService.objectToExcelURL(rawExcelData);
    if (url?.message) return url;

    const reportHistory = await this.pg.create(ReportHistoryEntity, {
      downloadUrl: url,
      fromDate: reqData.startDate,
      toDate: reqData.endDate,
      status: '1',
      adminId: reqData.headerAdminId ?? reqData.adminId,
      reportName: 'StageWiseLeadsData.xlsx',
      apiUrl: reqData.apiUrl,
    });
    console.log({ reportHistory });
    return { fileUrl: url };
  }
  // #endregion
}
