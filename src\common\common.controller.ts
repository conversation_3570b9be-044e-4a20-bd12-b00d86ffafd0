import { Body, Controller, Get, Post } from '@nestjs/common';
import { NotificationService } from 'src/notification/notification.service';

@Controller('common')
export class CommonController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get('templatesList')
  async getTemplatesList() {
    return this.notificationService.getTemplatesList();
  }

  @Post('manageTemplate')
  async funManageTemplate(@Body() body) {
    return this.notificationService.manageTemplate(body);
  }
}
