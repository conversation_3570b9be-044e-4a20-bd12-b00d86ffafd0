// Imports
import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { registeredUsers } from './registeredUsers';
import { MasterEntity } from './master.entity';

@Table({})
export class employmentDetails extends Model<employmentDetails> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  companyName: string;

  @ForeignKey(() => MasterEntity)
  @Column({ type: DataType.INTEGER, allowNull: true })
  masterId: number;

  @BelongsTo(() => MasterEntity)
  masterData: MasterEntity;

  @BelongsTo(() => registeredUsers)
  user: registeredUsers;
}
